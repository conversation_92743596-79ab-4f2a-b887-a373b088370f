{"productStructures": [{"id": "struct_001", "code": "FIRE_WINDOW_STD", "name": "标准防火窗结构", "description": "标准防火窗产品结构，支持单扇或双扇配置", "productType": "window", "category": "防火窗", "subCategory": "固定式防火窗", "rootAssembly": {"id": "rai_001", "assemblyId": "asm_001", "assemblyCode": "FRAME_MAIN_ASSEMBLY", "assemblyName": "主框架构件", "parameterValues": {"frameWidth": 80, "cornerJointType": "welded"}, "position": {"x": 0, "y": 0, "z": 0, "rotation": 0}, "quantity": 1, "quantityFormula": "1", "optional": false}, "productParameters": [{"id": "prod_param_001", "name": "window_width", "displayName": "窗宽", "type": "number", "unit": "mm", "defaultValue": 1500, "minValue": 600, "maxValue": 3000, "required": true, "description": "防火窗的净宽度", "category": "dimension"}, {"id": "prod_param_002", "name": "window_height", "displayName": "窗高", "type": "number", "unit": "mm", "defaultValue": 1800, "minValue": 800, "maxValue": 2400, "required": true, "description": "防火窗的净高度", "category": "dimension"}, {"id": "prod_param_003", "name": "fire_rating", "displayName": "防火等级", "type": "select", "defaultValue": "A", "options": [{"value": "A", "label": "A级防火"}, {"value": "B", "label": "B级防火"}, {"value": "C", "label": "C级防火"}], "required": true, "description": "防火窗的防火等级", "category": "quality"}, {"id": "prod_param_004", "name": "opening_type", "displayName": "开启方式", "type": "select", "defaultValue": "fixed", "options": [{"value": "fixed", "label": "固定式"}, {"value": "casement", "label": "平开式"}, {"value": "sliding", "label": "推拉式"}], "required": true, "description": "防火窗的开启方式", "category": "process"}, {"id": "prod_param_005", "name": "glass_configuration", "displayName": "玻璃配置", "type": "select", "defaultValue": "single_6mm", "options": [{"value": "single_6mm", "label": "6mm单片防火玻璃"}, {"value": "single_8mm", "label": "8mm单片防火玻璃"}, {"value": "laminated_6_6", "label": "6+1.52PVB+6夹胶防火玻璃"}, {"value": "insulated_6_12_6", "label": "6+12A+6中空防火玻璃"}], "required": true, "description": "防火玻璃的结构配置", "category": "material"}], "productConstraints": [{"id": "prod_const_001", "name": "尺寸约束", "type": "dimension", "expression": "window_width >= 600 && window_width <= 3000 && window_height >= 800 && window_height <= 2400", "errorMessage": "窗户尺寸超出标准范围", "severity": "error"}, {"id": "prod_const_002", "name": "大尺寸加强约束", "type": "dimension", "expression": "window_width * window_height > 4000000 ? frame_thickness >= 5.0 : true", "errorMessage": "大尺寸窗户需要加强型材", "severity": "warning", "autoFix": {"enabled": true, "fixExpression": "frame_thickness = 5.0", "fixMessage": "自动调整为加强型材"}}, {"id": "prod_const_003", "name": "防火等级匹配", "type": "material", "expression": "fire_rating === 'A' ? glass_fire_resistance_time >= 60 : glass_fire_resistance_time >= 30", "errorMessage": "防火等级与玻璃耐火时间不匹配", "severity": "error"}], "configurationOptions": [{"id": "config_opt_001", "optionName": "标准配置", "description": "适用于一般建筑的标准防火窗配置", "isDefault": true, "configurationChoices": [{"id": "choice_001", "choiceName": "标准框架", "parameterOverrides": {"frame_width": 80, "frame_thickness": 4.0, "material_grade": "Q355B"}, "componentChanges": [], "assemblyChanges": []}, {"id": "choice_002", "choiceName": "标准玻璃", "parameterOverrides": {"glass_thickness": 6, "glass_type": "single", "fire_resistance_time": 60}, "componentChanges": [], "assemblyChanges": []}]}, {"id": "config_opt_002", "optionName": "加强配置", "description": "适用于大尺寸或高要求的加强型防火窗", "isDefault": false, "configurationChoices": [{"id": "choice_003", "choiceName": "加强框架", "parameterOverrides": {"frame_width": 100, "frame_thickness": 5.0, "material_grade": "Q355B"}, "componentChanges": [], "assemblyChanges": []}, {"id": "choice_004", "choiceName": "夹胶玻璃", "parameterOverrides": {"glass_type": "laminated", "fire_resistance_time": 90}, "componentChanges": [{"componentId": "comp_003", "changeType": "parameter_override", "newParameters": {"glassType": "laminated", "fireResistanceTime": 90}}], "assemblyChanges": []}]}], "versionHistory": [{"version": 1, "changeDescription": "初始版本创建", "changedBy": "engineer_001", "changedAt": "2024-01-01T00:00:00Z", "changeType": "create", "changes": []}], "applications": ["住宅建筑", "办公建筑", "商业建筑", "工业建筑"], "tags": ["防火", "标准", "固定式", "单扇", "双扇"], "version": 1, "status": "active", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001"}, {"id": "struct_002", "code": "FIRE_PARTITION_STD", "name": "标准防火隔断结构", "description": "标准防火隔断产品结构，适用于室内空间分隔", "productType": "partition", "category": "防火隔断", "subCategory": "固定式隔断", "rootAssembly": {"id": "rai_002", "assemblyId": "asm_001", "assemblyCode": "FRAME_MAIN_ASSEMBLY", "assemblyName": "主框架构件", "parameterValues": {"frameWidth": 100, "cornerJointType": "welded"}, "position": {"x": 0, "y": 0, "z": 0, "rotation": 0}, "quantity": 1, "quantityFormula": "1", "optional": false}, "productParameters": [{"id": "prod_param_006", "name": "partition_width", "displayName": "隔断宽度", "type": "number", "unit": "mm", "defaultValue": 3000, "minValue": 1000, "maxValue": 6000, "required": true, "description": "防火隔断的宽度", "category": "dimension"}, {"id": "prod_param_007", "name": "partition_height", "displayName": "隔断高度", "type": "number", "unit": "mm", "defaultValue": 2400, "minValue": 2000, "maxValue": 3600, "required": true, "description": "防火隔断的高度", "category": "dimension"}, {"id": "prod_param_008", "name": "frame_material", "displayName": "框架材质", "type": "select", "defaultValue": "stainless_steel_304", "options": [{"value": "stainless_steel_304", "label": "304不锈钢"}, {"value": "stainless_steel_316", "label": "316不锈钢"}, {"value": "aluminum_alloy", "label": "铝合金"}, {"value": "steel_painted", "label": "喷涂钢材"}], "required": true, "description": "隔断框架的材质", "category": "material"}], "productConstraints": [{"id": "prod_const_004", "name": "隔断尺寸约束", "type": "dimension", "expression": "partition_width >= 1000 && partition_width <= 6000 && partition_height >= 2000 && partition_height <= 3600", "errorMessage": "隔断尺寸超出标准范围", "severity": "error"}], "configurationOptions": [{"id": "config_opt_003", "optionName": "酒店隔断配置", "description": "适用于酒店大堂、餐厅等场所的高端隔断配置", "isDefault": true, "configurationChoices": [{"id": "choice_005", "choiceName": "不锈钢框架", "parameterOverrides": {"frame_material": "stainless_steel_304", "frame_surface": "brushed", "frame_width": 100, "frame_thickness": 5.0}, "componentChanges": [], "assemblyChanges": []}]}], "versionHistory": [{"version": 1, "changeDescription": "初始版本创建", "changedBy": "engineer_001", "changedAt": "2024-01-01T00:00:00Z", "changeType": "create", "changes": []}], "applications": ["酒店", "办公楼", "商场", "展厅"], "tags": ["防火", "隔断", "不锈钢", "高端"], "version": 1, "status": "active", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001"}, {"id": "struct_003", "code": "CURTAIN_WALL_STD", "name": "标准幕墙结构", "description": "标准玻璃幕墙产品结构，适用于建筑外立面", "productType": "curtain_wall", "category": "玻璃幕墙", "subCategory": "框架式幕墙", "rootAssembly": {"id": "rai_003", "assemblyId": "asm_001", "assemblyCode": "FRAME_MAIN_ASSEMBLY", "assemblyName": "主框架构件", "parameterValues": {"frameWidth": 120, "cornerJointType": "bolted"}, "position": {"x": 0, "y": 0, "z": 0, "rotation": 0}, "quantity": 1, "quantityFormula": "1", "optional": false}, "productParameters": [{"id": "prod_param_009", "name": "panel_width", "displayName": "面板宽度", "type": "number", "unit": "mm", "defaultValue": 1500, "minValue": 800, "maxValue": 2400, "required": true, "description": "幕墙面板的宽度", "category": "dimension"}, {"id": "prod_param_010", "name": "panel_height", "displayName": "面板高度", "type": "number", "unit": "mm", "defaultValue": 3600, "minValue": 2400, "maxValue": 4800, "required": true, "description": "幕墙面板的高度", "category": "dimension"}, {"id": "prod_param_011", "name": "glass_type", "displayName": "玻璃类型", "type": "select", "defaultValue": "insulated_double", "options": [{"value": "single", "label": "单层玻璃"}, {"value": "insulated_double", "label": "双层中空玻璃"}, {"value": "insulated_triple", "label": "三层中空玻璃"}, {"value": "laminated", "label": "夹胶玻璃"}], "required": true, "description": "幕墙使用的玻璃类型", "category": "material"}], "productConstraints": [{"id": "prod_const_005", "name": "幕墙尺寸约束", "type": "dimension", "expression": "panel_width >= 800 && panel_width <= 2400 && panel_height >= 2400 && panel_height <= 4800", "errorMessage": "幕墙面板尺寸超出标准范围", "severity": "error"}, {"id": "prod_const_006", "name": "高层建筑约束", "type": "material", "expression": "building_height > 50000 ? glass_type !== 'single' : true", "errorMessage": "高层建筑不能使用单层玻璃", "severity": "error"}], "configurationOptions": [{"id": "config_opt_004", "optionName": "标准幕墙配置", "description": "适用于一般商业建筑的标准幕墙配置", "isDefault": true, "configurationChoices": [{"id": "choice_006", "choiceName": "铝合金框架", "parameterOverrides": {"frame_material": "aluminum_alloy", "frame_surface": "anodized", "frame_width": 120, "frame_thickness": 6.0}, "componentChanges": [], "assemblyChanges": []}]}], "versionHistory": [{"version": 1, "changeDescription": "初始版本创建", "changedBy": "engineer_002", "changedAt": "2024-01-15T00:00:00Z", "changeType": "create", "changes": []}], "applications": ["商业建筑", "办公楼", "酒店", "购物中心"], "tags": ["幕墙", "玻璃", "铝合金", "外立面"], "version": 1, "status": "active", "createdAt": "2024-01-15T00:00:00Z", "updatedAt": "2024-01-15T00:00:00Z", "createdBy": "engineer_002", "updatedBy": "engineer_002"}]}